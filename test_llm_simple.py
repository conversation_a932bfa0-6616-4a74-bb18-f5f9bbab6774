"""
简化的LLM测试脚本

专门测试LLM配置和连接，避免数据库等其他依赖。
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.config import get_settings
from src.core.llm_manager import LLMManager, quick_chat


async def test_config_loading():
    """测试配置加载"""
    print("=" * 60)
    print("测试1: 配置加载")
    print("=" * 60)
    
    try:
        settings = get_settings()
        
        print("✓ 配置加载成功")
        print(f"  - 推理模型: {settings.reasoning_llm.model}")
        print(f"  - 基础模型: {settings.basic_llm.model}")
        print(f"  - 地图模型: {settings.map_llm.model}")
        print(f"  - API基础URL: {settings.reasoning_llm.base_url}")
        
        # 检查API Key是否存在（不显示完整内容）
        api_key = settings.reasoning_llm.api_key
        if api_key:
            print(f"  - API Key: {api_key[:10]}...{api_key[-4:]} (已配置)")
        else:
            print("  - API Key: 未配置")
            
        return True
        
    except Exception as e:
        print(f"✗ 配置加载失败: {str(e)}")
        return False


async def test_basic_llm():
    """测试基础LLM连接"""
    print("\n" + "=" * 60)
    print("测试2: 基础LLM连接测试")
    print("=" * 60)
    
    try:
        manager = LLMManager()
        
        print("测试基础模型连接...")
        response = await manager.chat(
            message="你好，请简单回复一句话确认连接正常。",
            role="basic"
        )
        
        print("✓ 基础模型连接成功")
        print(f"  - 响应内容: {response.get('content', '')}")
        print(f"  - 使用Token: {response.get('usage', {}).get('total_tokens', 0)}")
        
        await manager.close_all()
        return True
        
    except Exception as e:
        print(f"✗ 基础LLM连接失败: {str(e)}")
        return False


async def test_reasoning_llm():
    """测试推理LLM连接"""
    print("\n" + "=" * 60)
    print("测试3: 推理LLM连接测试")
    print("=" * 60)
    
    try:
        manager = LLMManager()
        
        print("测试推理模型连接...")
        response = await manager.chat(
            message="请用一句话说明你是什么模型。",
            role="reasoning"
        )
        
        print("✓ 推理模型连接成功")
        print(f"  - 响应内容: {response.get('content', '')}")
        print(f"  - 使用Token: {response.get('usage', {}).get('total_tokens', 0)}")
        
        await manager.close_all()
        return True
        
    except Exception as e:
        print(f"✗ 推理LLM连接失败: {str(e)}")
        return False


async def test_travel_planning_reasoning():
    """测试旅行规划推理能力"""
    print("\n" + "=" * 60)
    print("测试4: 旅行规划推理能力")
    print("=" * 60)
    
    try:
        # 测试简单的旅行规划推理
        test_query = """
        我想去北京玩3天，主要想看故宫、长城这些历史文化景点。
        请分析我的旅行意图，包括：
        1. 目的地
        2. 天数
        3. 旅行主题
        4. 主要兴趣点
        
        请用JSON格式回复，包含以上信息。
        """
        
        print("发送旅行规划测试查询...")
        response = await quick_chat(
            message=test_query,
            role="basic"  # 使用基础模型，避免推理模型的网络问题
        )
        
        content = response.get('content', '')
        print("✓ 旅行规划推理成功")
        print(f"  - 响应长度: {len(content)} 字符")
        print(f"  - 使用Token: {response.get('usage', {}).get('total_tokens', 0)}")
        print(f"  - 响应内容:")
        print(f"    {content}")
        
        return True
        
    except Exception as e:
        print(f"✗ 旅行规划推理失败: {str(e)}")
        return False


async def test_intent_analysis():
    """测试意图分析能力"""
    print("\n" + "=" * 60)
    print("测试5: 意图分析能力")
    print("=" * 60)
    
    try:
        # 测试意图分析
        test_query = """
        请分析以下旅行查询的核心意图：
        "我想开车去杭州玩2天，主要想看西湖和周边的自然风光，预算不超过2000元"
        
        请提取：
        1. 交通方式
        2. 目的地
        3. 天数
        4. 兴趣类型
        5. 预算限制
        
        用简洁的格式回复。
        """
        
        print("测试意图分析...")
        response = await quick_chat(
            message=test_query,
            role="basic"
        )
        
        content = response.get('content', '')
        print("✓ 意图分析成功")
        print(f"  - 响应内容:")
        print(f"    {content}")
        
        return True
        
    except Exception as e:
        print(f"✗ 意图分析失败: {str(e)}")
        return False


async def test_multi_turn_conversation():
    """测试多轮对话能力"""
    print("\n" + "=" * 60)
    print("测试6: 多轮对话能力")
    print("=" * 60)
    
    try:
        manager = LLMManager()
        
        # 第一轮对话
        print("第一轮: 询问旅行建议...")
        response1 = await manager.chat(
            message="我想去一个有山有水的地方旅行，你有什么推荐吗？",
            role="basic"
        )
        print(f"  回复: {response1.get('content', '')[:100]}...")
        
        # 第二轮对话（带历史）
        print("第二轮: 询问具体信息...")
        response2 = await manager.chat(
            message="那杭州怎么样？需要几天时间？",
            role="basic",
            history=[
                {"role": "user", "content": "我想去一个有山有水的地方旅行，你有什么推荐吗？"},
                {"role": "assistant", "content": response1.get('content', '')}
            ]
        )
        print(f"  回复: {response2.get('content', '')[:100]}...")
        
        print("✓ 多轮对话测试成功")
        
        await manager.close_all()
        return True
        
    except Exception as e:
        print(f"✗ 多轮对话测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("AutoPilot AI - 简化LLM功能测试")
    print("=" * 60)
    print("本测试将验证:")
    print("1. 配置文件加载")
    print("2. 基础LLM连接")
    print("3. 推理LLM连接")
    print("4. 旅行规划推理")
    print("5. 意图分析能力")
    print("6. 多轮对话能力")
    print("=" * 60)
    
    results = []
    
    try:
        # 运行所有测试
        results.append(await test_config_loading())
        results.append(await test_basic_llm())
        results.append(await test_reasoning_llm())
        results.append(await test_travel_planning_reasoning())
        results.append(await test_intent_analysis())
        results.append(await test_multi_turn_conversation())
        
        # 总结结果
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        test_names = [
            "配置加载",
            "基础LLM连接",
            "推理LLM连接",
            "旅行规划推理",
            "意图分析能力",
            "多轮对话能力"
        ]
        
        passed = sum(results)
        total = len(results)
        
        for i, (name, result) in enumerate(zip(test_names, results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{i+1}. {name}: {status}")
        
        print(f"\n总体结果: {passed}/{total} 测试通过")
        
        if passed >= 4:  # 至少4个测试通过就认为基本可用
            print("🎉 LLM功能基本正常! 可以进行旅行规划。")
            if passed < total:
                print("💡 部分高级功能可能需要网络优化。")
        else:
            print("⚠️  LLM功能存在问题，请检查配置和网络连接。")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
